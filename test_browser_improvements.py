#!/usr/bin/env python3
"""
Test script to verify browser helper improvements:
1. Fixed use_planner parameter error
2. URL parameter support
3. Browser-use memory integration
4. Initial actions optimization
"""

import os
import sys
sys.path.append('.')

from src.Utilities.browser_helper import (
    create_fast_config,
    create_robust_config,
    extract_urls_from_scenario,
    create_initial_actions_from_url,
    validate_config,
    BrowserHelperConfig
)

def test_config_improvements():
    """Test that configuration improvements work correctly"""
    print("🧪 Testing configuration improvements...")
    
    try:
        # Test fast config (for smoke tests)
        fast_config = create_fast_config(
            headless=True,
            use_vision=True,
            enable_memory=False  # Disabled for speed
        )
        
        print("✅ Fast config created successfully")
        print(f"  - Vision: {fast_config.use_vision}")
        print(f"  - Memory: {fast_config.enable_memory}")
        print(f"  - Memory interval: {fast_config.memory_interval}")
        print(f"  - Planner LLM: {fast_config.planner_llm}")
        print(f"  - Initial actions: {fast_config.initial_actions}")
        
        # Test robust config (for full tests with memory)
        robust_config = create_robust_config(
            memory_agent_id="test_agent_123",
            memory_interval=15
        )
        
        print("\n✅ Robust config created successfully")
        print(f"  - Vision: {robust_config.use_vision}")
        print(f"  - Memory: {robust_config.enable_memory}")
        print(f"  - Memory agent ID: {robust_config.memory_agent_id}")
        print(f"  - Memory interval: {robust_config.memory_interval}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_url_handling():
    """Test URL extraction and initial actions"""
    print("\n🧪 Testing URL handling...")
    
    try:
        # Test URL extraction from Gherkin
        scenario = '''
        Feature: Test navigation
        
        Scenario: Navigate to example site
            Given que el usuario navega a "https://example.com"
            When el usuario ve la página principal
            Then la página debe cargar correctamente
        '''
        
        urls = extract_urls_from_scenario(scenario)
        print(f"✅ URLs extracted: {urls}")
        
        if urls:
            initial_actions = create_initial_actions_from_url(urls[0])
            print(f"✅ Initial actions created: {initial_actions}")
            
            expected_action = {'open_tab': {'url': 'https://example.com'}}
            if initial_actions == [expected_action]:
                print("✅ Initial actions format is correct")
            else:
                print(f"⚠️ Unexpected initial actions format: {initial_actions}")
        
        # Test direct URL parameter (simulating test_executor usage)
        direct_url = "https://google.com"
        direct_actions = create_initial_actions_from_url(direct_url)
        print(f"✅ Direct URL actions: {direct_actions}")
        
        return True
    except Exception as e:
        print(f"❌ URL handling test failed: {e}")
        return False

def test_memory_config():
    """Test browser-use memory configuration"""
    print("\n🧪 Testing memory configuration...")
    
    try:
        # Test memory-enabled config
        memory_config = BrowserHelperConfig(
            enable_memory=True,
            memory_agent_id="test_memory_agent",
            memory_interval=5
        )
        
        print("✅ Memory config created successfully")
        print(f"  - Memory enabled: {memory_config.enable_memory}")
        print(f"  - Agent ID: {memory_config.memory_agent_id}")
        print(f"  - Memory interval: {memory_config.memory_interval}")
        
        # Test validation
        warnings = validate_config(memory_config)
        print(f"✅ Config validation completed with {len(warnings)} warnings")
        for warning in warnings:
            print(f"  ⚠️ {warning}")
        
        return True
    except Exception as e:
        print(f"❌ Memory config test failed: {e}")
        return False

def test_backwards_compatibility():
    """Test that old code still works"""
    print("\n🧪 Testing backwards compatibility...")
    
    try:
        # Test old-style config creation (should still work)
        old_style_config = create_fast_config(
            headless=True,
            use_vision=True
            # Note: use_planner is no longer used, but this should still work
        )
        
        print("✅ Old-style config creation works")
        print(f"  - Vision: {old_style_config.use_vision}")
        print(f"  - Memory: {old_style_config.enable_memory}")
        
        return True
    except Exception as e:
        print(f"❌ Backwards compatibility test failed: {e}")
        return False

def main():
    """Run all improvement tests"""
    print("🔧 Testing Browser Helper Improvements")
    print("=" * 60)
    
    tests = [
        test_config_improvements,
        test_url_handling,
        test_memory_config,
        test_backwards_compatibility
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All {total} tests passed!")
        print("\n✅ Browser helper improvements are working correctly:")
        print("  - ❌ use_planner parameter removed (fixed error)")
        print("  - ✅ URL parameter support added")
        print("  - ✅ Browser-use memory integration implemented")
        print("  - ✅ Initial actions auto-generation working")
        print("  - ✅ Backwards compatibility maintained")
        print("\n🚀 Ready for smoke tests and full tests!")
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
