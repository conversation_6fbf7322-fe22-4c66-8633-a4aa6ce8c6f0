"""
Configuraciones predefinidas para diferentes contextos de uso del browser helper.
Compatible con browser-use 0.2.4
"""

import os
from typing import List, Optional

try:
    from src.Utilities.browser_helper import BrowserHelperConfig
except ImportError:
    # Fallback if there are import issues
    class BrowserHelperConfig:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)


class BrowserConfigurations:
    """Clase con configuraciones predefinidas para diferentes escenarios."""

    @staticmethod
    def get_ci_cd_config(**overrides) -> BrowserHelperConfig:
        """Configuración optimizada para CI/CD - velocidad máxima."""
        defaults = {
            "headless": True,
            "use_vision": False,  # Desactivar visión para máxima velocidad
            "enable_memory": False,  # Sin memoria para velocidad
            "deterministic_rendering": False,
            "highlight_elements": False,
            "viewport_expansion": 0,
            "minimum_wait_page_load_time": 0.1,
            "wait_for_network_idle_page_load_time": 0.2,
            "maximum_wait_page_load_time": 3.0,
            "wait_between_actions": 0.05,
            "max_steps": 25,
            "max_failures": 1,  # Fallar rápido en CI/CD
            "retry_delay": 2,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_smoke_test_config(**overrides) -> BrowserHelperConfig:
        """Configuración para smoke tests - balance entre velocidad y confiabilidad."""
        defaults = {
            "headless": True,
            "use_vision": True,
            "enable_memory": False,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 300,
            "minimum_wait_page_load_time": 0.3,
            "wait_for_network_idle_page_load_time": 0.5,
            "maximum_wait_page_load_time": 8.0,
            "wait_between_actions": 0.2,
            "max_steps": 30,
            "max_failures": 2,
            "retry_delay": 3,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_regression_test_config(**overrides) -> BrowserHelperConfig:
        """Configuración para tests de regresión - máxima confiabilidad."""
        defaults = {
            "headless": True,
            "use_vision": True,
            "enable_memory": True,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 800,
            "minimum_wait_page_load_time": 1.0,
            "wait_for_network_idle_page_load_time": 2.0,
            "maximum_wait_page_load_time": 20.0,
            "wait_between_actions": 1.0,
            "max_steps": 200,
            "max_failures": 5,
            "retry_delay": 10,
            "model_provider": "gemini",
            "temperature": 0.0,
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_development_config(**overrides) -> BrowserHelperConfig:
        """Configuración para desarrollo - debugging y observación."""
        defaults = {
            "headless": False,  # Visual para desarrollo
            "use_vision": True,
            "enable_memory": False,  # Simplificar para debugging
            "deterministic_rendering": True,
            "highlight_elements": True,
            "viewport_expansion": 1000,
            "minimum_wait_page_load_time": 1.0,
            "wait_for_network_idle_page_load_time": 2.0,
            "maximum_wait_page_load_time": 30.0,
            "wait_between_actions": 2.0,  # Lento para observación
            "max_steps": 50,
            "max_failures": 1,  # Fallar rápido para debugging
            "retry_delay": 5,
            "model_provider": "gemini",
            "temperature": 0.0,
            "save_conversation_path": "./debug_conversations",
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_production_config(allowed_domains: List[str], **overrides) -> BrowserHelperConfig:
        """Configuración para producción - seguridad y estabilidad."""
        defaults = {
            "headless": True,
            "allowed_domains": allowed_domains,
            "disable_security": False,
            "use_vision": True,
            "enable_memory": True,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 500,
            "minimum_wait_page_load_time": 0.8,
            "wait_for_network_idle_page_load_time": 1.5,
            "maximum_wait_page_load_time": 15.0,
            "wait_between_actions": 0.8,
            "max_steps": 100,
            "max_failures": 3,
            "retry_delay": 8,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_web_interface_config(**overrides) -> BrowserHelperConfig:
        """Configuración para interfaz web - balance para usuarios."""
        defaults = {
            "headless": True,
            "use_vision": True,
            "enable_memory": True,
            "deterministic_rendering": True,
            "highlight_elements": False,
            "viewport_expansion": 600,
            "minimum_wait_page_load_time": 0.6,
            "wait_for_network_idle_page_load_time": 1.2,
            "maximum_wait_page_load_time": 12.0,
            "wait_between_actions": 0.6,
            "max_steps": 120,
            "max_failures": 3,
            "retry_delay": 8,
            "model_provider": "gemini",
            "temperature": 0.0,
            "generate_gif": False,
            "save_conversation_path": "./web_conversations"
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_api_testing_config(**overrides) -> BrowserHelperConfig:
        """Configuración para testing de APIs - enfoque en funcionalidad."""
        defaults = {
            "headless": True,
            "use_vision": False,  # APIs no necesitan visión
            "enable_memory": False,
            "deterministic_rendering": False,
            "highlight_elements": False,
            "viewport_expansion": 0,
            "minimum_wait_page_load_time": 0.2,
            "wait_for_network_idle_page_load_time": 0.5,
            "maximum_wait_page_load_time": 5.0,
            "wait_between_actions": 0.1,
            "max_steps": 40,
            "max_failures": 2,
            "retry_delay": 3,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_load_testing_config(**overrides) -> BrowserHelperConfig:
        """Configuración para load testing - mínimo overhead."""
        defaults = {
            "headless": True,
            "use_vision": False,
            "enable_memory": False,
            "deterministic_rendering": False,
            "highlight_elements": False,
            "viewport_expansion": 0,
            "minimum_wait_page_load_time": 0.05,
            "wait_for_network_idle_page_load_time": 0.1,
            "maximum_wait_page_load_time": 2.0,
            "wait_between_actions": 0.01,
            "max_steps": 15,
            "max_failures": 1,
            "retry_delay": 1,
            "model_provider": "gemini",
            "temperature": 0.0
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)


class EnvironmentBasedConfig:
    """Configuraciones basadas en variables de entorno."""

    @staticmethod
    def get_config_for_environment() -> BrowserHelperConfig:
        """Obtiene configuración basada en variables de entorno."""
        env = os.getenv("BROWSER_TEST_ENV", "development").lower()

        if env == "ci":
            return BrowserConfigurations.get_ci_cd_config()
        elif env == "production":
            allowed_domains = os.getenv("ALLOWED_DOMAINS", "").split(",")
            allowed_domains = [d.strip() for d in allowed_domains if d.strip()]
            return BrowserConfigurations.get_production_config(allowed_domains)
        elif env == "staging":
            return BrowserConfigurations.get_regression_test_config(
                headless=True,
                max_steps=150
            )
        else:  # development
            return BrowserConfigurations.get_development_config()

    @staticmethod
    def get_model_provider_from_env() -> str:
        """Obtiene el proveedor de modelo desde variables de entorno."""
        if os.getenv("OPENAI_API_KEY"):
            return "openai"
        elif os.getenv("ANTHROPIC_API_KEY"):
            return "anthropic"
        elif os.getenv("GOOGLE_API_KEY"):
            return "gemini"
        else:
            return "gemini"  # Default


# Configuraciones de conveniencia
def get_config_by_type(config_type: str, **overrides) -> BrowserHelperConfig:
    """
    Obtiene configuración por tipo.

    Args:
        config_type: Tipo de configuración (ci, smoke, regression, dev, prod, web, api, load)
        **overrides: Parámetros para sobrescribir

    Returns:
        BrowserHelperConfig configurado
    """
    config_map = {
        "ci": BrowserConfigurations.get_ci_cd_config,
        "smoke": BrowserConfigurations.get_smoke_test_config,
        "regression": BrowserConfigurations.get_regression_test_config,
        "dev": BrowserConfigurations.get_development_config,
        "web": BrowserConfigurations.get_web_interface_config,
        "api": BrowserConfigurations.get_api_testing_config,
        "load": BrowserConfigurations.get_load_testing_config
    }

    if config_type == "prod":
        allowed_domains = overrides.pop("allowed_domains", [])
        return BrowserConfigurations.get_production_config(allowed_domains, **overrides)

    config_func = config_map.get(config_type, BrowserConfigurations.get_development_config)
    return config_func(**overrides)
