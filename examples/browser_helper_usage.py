#!/usr/bin/env python3
"""
Ejemplos de uso del Browser Helper mejorado con browser-use 0.2.4+

Este archivo demuestra cómo usar las nuevas funcionalidades del browser helper
con configuraciones predefinidas y características avanzadas.
"""

import asyncio
import os
from src.Utilities.browser_helper import (
    create_and_run_agent,
    BrowserHelperConfig,
    create_fast_config,
    create_robust_config,
    create_secure_config,
    create_debug_config,
    validate_config
)
from src.Utilities.utils import controller


async def example_basic_usage():
    """Ejemplo básico - uso sin configuración (mantiene compatibilidad)"""
    print("🔹 Ejemplo básico - uso sin configuración")
    
    scenario = """
    Feature: Búsqueda básica en Google
    Scenario: Buscar información
        Given I am on "https://google.com"
        When I search for "browser automation"
        Then I should see search results
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            language="en"
        )
        print("✅ Ejecución básica completada")
        return history
    except Exception as e:
        print(f"❌ Error en ejecución básica: {e}")
        return None


async def example_fast_config():
    """Ejemplo con configuración rápida para CI/CD"""
    print("🔹 Ejemplo con configuración rápida")
    
    # Crear configuración optimizada para velocidad
    config = create_fast_config(
        headless=True,
        max_steps=20,  # Limitar pasos para velocidad
        viewport_expansion=200,  # Menor contexto para velocidad
        wait_between_actions=0.1  # Acciones más rápidas
    )
    
    # Validar configuración
    warnings = validate_config(config)
    if warnings:
        print(f"⚠️ Warnings: {warnings}")
    
    scenario = """
    Feature: Test rápido
    Scenario: Verificar página principal
        Given I am on "https://example.com"
        Then I should see the page title
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=config,
            language="en"
        )
        print("✅ Ejecución rápida completada")
        return history
    except Exception as e:
        print(f"❌ Error en ejecución rápida: {e}")
        return None


async def example_robust_config():
    """Ejemplo con configuración robusta para tests completos"""
    print("🔹 Ejemplo con configuración robusta")
    
    # Crear configuración optimizada para robustez
    config = create_robust_config(
        headless=True,
        max_steps=100,
        viewport_expansion=800,  # Mayor contexto
        wait_between_actions=1.0,  # Más tiempo entre acciones
        save_conversation_path="./test_conversations"  # Guardar conversaciones
    )
    
    scenario = """
    Feature: Test completo de formulario
    Scenario: Llenar formulario complejo
        Given I am on "https://httpbin.org/forms/post"
        When I fill in the form with test data
        And I submit the form
        Then I should see a success message
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=config,
            language="en"
        )
        print("✅ Ejecución robusta completada")
        return history
    except Exception as e:
        print(f"❌ Error en ejecución robusta: {e}")
        return None


async def example_secure_config():
    """Ejemplo con configuración segura para producción"""
    print("🔹 Ejemplo con configuración segura")
    
    # Crear configuración con restricciones de seguridad
    allowed_domains = ["https://httpbin.org", "https://*.github.com"]
    config = create_secure_config(
        allowed_domains=allowed_domains,
        headless=True,
        disable_security=False,  # Mantener seguridad habilitada
        max_steps=50
    )
    
    scenario = """
    Feature: Test seguro
    Scenario: Acceder solo a dominios permitidos
        Given I am on "https://httpbin.org/get"
        Then I should see the JSON response
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=config,
            language="en"
        )
        print("✅ Ejecución segura completada")
        return history
    except Exception as e:
        print(f"❌ Error en ejecución segura: {e}")
        return None


async def example_debug_config():
    """Ejemplo con configuración de debugging"""
    print("🔹 Ejemplo con configuración de debugging")
    
    # Crear configuración optimizada para debugging
    config = create_debug_config(
        headless=False,  # Modo visual para debugging
        max_steps=20,
        wait_between_actions=3.0,  # Más lento para observar
        highlight_elements=True,  # Resaltar elementos
        save_conversation_path="./debug_conversations"
    )
    
    scenario = """
    Feature: Debug test
    Scenario: Test con debugging visual
        Given I am on "https://example.com"
        When I interact with the page
        Then I should see the results
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=config,
            language="en"
        )
        print("✅ Ejecución de debugging completada")
        return history
    except Exception as e:
        print(f"❌ Error en ejecución de debugging: {e}")
        return None


async def example_custom_config():
    """Ejemplo con configuración personalizada"""
    print("🔹 Ejemplo con configuración personalizada")
    
    # Crear configuración completamente personalizada
    config = BrowserHelperConfig(
        headless=True,
        use_vision=True,
        max_steps=75,
        model_provider="gemini",
        temperature=0.1,
        
        # Configuraciones de browser
        viewport={"width": 1920, "height": 1080},
        device_scale_factor=2,  # Alta resolución
        
        # Configuraciones de timing
        minimum_wait_page_load_time=1.0,
        wait_for_network_idle_page_load_time=2.0,
        maximum_wait_page_load_time=15.0,
        wait_between_actions=1.5,
        
        # Configuraciones avanzadas
        viewport_expansion=1000,
        deterministic_rendering=True,
        highlight_elements=True,
        
        # Configuraciones de sesión
        keep_alive=False,
        save_conversation_path="./custom_conversations"
    )
    
    # Validar configuración personalizada
    warnings = validate_config(config)
    if warnings:
        print(f"⚠️ Warnings en configuración personalizada: {warnings}")
    
    scenario = """
    Feature: Test personalizado
    Scenario: Test con configuración específica
        Given I am on "https://httpbin.org/html"
        When I interact with the HTML content
        Then I should see the expected results
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=config,
            language="en"
        )
        print("✅ Ejecución personalizada completada")
        return history
    except Exception as e:
        print(f"❌ Error en ejecución personalizada: {e}")
        return None


async def main():
    """Ejecutar todos los ejemplos"""
    print("🚀 Iniciando ejemplos de Browser Helper mejorado")
    print("=" * 60)
    
    # Verificar que las variables de entorno estén configuradas
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY no está configurada")
        return
    
    examples = [
        ("Básico", example_basic_usage),
        ("Rápido", example_fast_config),
        ("Robusto", example_robust_config),
        ("Seguro", example_secure_config),
        ("Debug", example_debug_config),
        ("Personalizado", example_custom_config)
    ]
    
    results = {}
    
    for name, example_func in examples:
        print(f"\n📋 Ejecutando ejemplo: {name}")
        print("-" * 40)
        
        try:
            result = await example_func()
            results[name] = "✅ Exitoso" if result else "❌ Falló"
        except Exception as e:
            results[name] = f"❌ Error: {str(e)[:50]}..."
        
        print(f"Resultado: {results[name]}")
    
    print("\n" + "=" * 60)
    print("📊 Resumen de resultados:")
    for name, result in results.items():
        print(f"  {name}: {result}")


if __name__ == "__main__":
    asyncio.run(main())
